package com.lx.pl.service;

import com.lx.pl.client.FluxApiClient;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.flux.FluxResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import retrofit2.Call;
import retrofit2.Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Flux服务测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class FluxServiceTest {

    @Mock
    private FluxApiClient fluxApiClient;

    @Mock
    private FluxConfig fluxConfig;

    @Mock
    private RedisService redisService;

    @InjectMocks
    private FluxService fluxService;

    private User testUser;
    private GenGenericPara testGenParameters;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setLoginName("<EMAIL>");

        testGenParameters = new GenGenericPara();
        testGenParameters.setPrompt("A beautiful landscape");
        testGenParameters.setAspectRatio("16:9");
        testGenParameters.setSeed(42);

        // Mock配置
        when(fluxConfig.getDefaultOutputFormat()).thenReturn("jpeg");
        when(fluxConfig.getDefaultSafetyTolerance()).thenReturn(2);
        when(fluxConfig.getPromptUpsamplingEnabled()).thenReturn(false);
        when(fluxConfig.getApiKey()).thenReturn("test-api-key");
    }

    @Test
    void testCreateKontextProTask_Success() throws Exception {
        // Arrange
        FluxResponse.CreateTaskResponse expectedResponse = new FluxResponse.CreateTaskResponse();
        expectedResponse.setId("test-task-id");
        expectedResponse.setPollingUrl("https://api.bfl.ai/v1/get_result?id=test-task-id");

        Call<FluxResponse.CreateTaskResponse> mockCall = mock(Call.class);
        Response<FluxResponse.CreateTaskResponse> mockResponse = Response.success(expectedResponse);

        when(fluxApiClient.createKontextProTask(anyString(), any())).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(mockResponse);

        // Act
        FluxResponse.CreateTaskResponse result = fluxService.createKontextProTask(
                "A beautiful landscape",
                testUser,
                null,
                false,
                "web",
                testGenParameters
        );

        // Assert
        assertNotNull(result);
        assertEquals("test-task-id", result.getId());
        assertEquals("https://api.bfl.ai/v1/get_result?id=test-task-id", result.getPollingUrl());

        // Verify API call
        verify(fluxApiClient).createKontextProTask(eq("test-api-key"), any());
        verify(mockCall).execute();
    }

    @Test
    void testCreateKontextProTask_ApiError() throws Exception {
        // Arrange
        Call<FluxResponse.CreateTaskResponse> mockCall = mock(Call.class);
        Response<FluxResponse.CreateTaskResponse> mockResponse = Response.error(400, 
                okhttp3.ResponseBody.create(null, "Bad Request"));

        when(fluxApiClient.createKontextProTask(anyString(), any())).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(mockResponse);

        // Act & Assert
        assertThrows(Exception.class, () -> {
            fluxService.createKontextProTask(
                    "A beautiful landscape",
                    testUser,
                    null,
                    false,
                    "web",
                    testGenParameters
            );
        });
    }

    @Test
    void testGetTaskResult_Success() throws Exception {
        // Arrange
        FluxResponse.TaskStatusResponse expectedResponse = new FluxResponse.TaskStatusResponse();
        expectedResponse.setId("test-task-id");
        expectedResponse.setStatus("Ready");
        expectedResponse.setProgress(100);

        FluxResponse.TaskResult taskResult = new FluxResponse.TaskResult();
        taskResult.setSample("https://example.com/image.jpg");
        taskResult.setWidth(1024);
        taskResult.setHeight(768);
        expectedResponse.setResult(taskResult);

        Call<FluxResponse.TaskStatusResponse> mockCall = mock(Call.class);
        Response<FluxResponse.TaskStatusResponse> mockResponse = Response.success(expectedResponse);

        when(fluxApiClient.getTaskResult("test-task-id")).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(mockResponse);

        // Act
        FluxResponse.TaskStatusResponse result = fluxService.getTaskResult("test-task-id");

        // Assert
        assertNotNull(result);
        assertEquals("test-task-id", result.getId());
        assertEquals("Ready", result.getStatus());
        assertEquals(100, result.getProgress());
        assertNotNull(result.getResult());
        assertEquals("https://example.com/image.jpg", result.getResult().getSample());

        // Verify API call
        verify(fluxApiClient).getTaskResult("test-task-id");
        verify(mockCall).execute();
    }

    @Test
    void testCheckFluxConcurrentJobs_WithinLimit() {
        // Arrange
        when(fluxConfig.getMaxConcurrentJobs()).thenReturn(10);
        when(redisService.getAllKeysFromHash(anyString())).thenReturn(java.util.Arrays.asList(
                "task1", "task2", "task3"
        ));

        // Act
        boolean result = fluxService.checkFluxConcurrentJobs(testUser, null);

        // Assert
        assertFalse(result); // Should not exceed limit
    }

    @Test
    void testCheckFluxConcurrentJobs_ExceedsLimit() {
        // Arrange
        when(fluxConfig.getMaxConcurrentJobs()).thenReturn(2);
        when(redisService.getAllKeysFromHash(anyString())).thenReturn(java.util.Arrays.asList(
                "task1", "task2", "task3"
        ));

        // Act
        boolean result = fluxService.checkFluxConcurrentJobs(testUser, null);

        // Assert
        assertTrue(result); // Should exceed limit
    }

    @Test
    void testRemoveFluxConcurrentJob() {
        // Act
        fluxService.removeFluxConcurrentJob("<EMAIL>", "test-task-id");

        // Assert
        verify(redisService).deleteFieldFromHash(anyString(), eq("test-task-id"));
    }
}
