package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.config.FluxConfig;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.flux.FluxResponse;
import com.lx.pl.enums.FluxTaskStatus;
import com.lx.pl.mq.NormalMessageProducer;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.RedisService.USER_TODAY_CREATE_IMG_NUMS;

/**
 * Flux回调处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxCallbackService {

    @Autowired
    private FluxService fluxService;

    @Autowired
    private FluxConfig fluxConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Value("${rocketmq.flux.polling.topic:tp_flux_polling_test}")
    private String fluxPollingTopic;

    @Value("${rocketmq.flux.polling.tag:tag_flux_polling_test}")
    private String fluxPollingTag;

    private static final String FLUX_TASK_PREFIX = "flux:task:";

    /**
     * 处理Flux任务轮询
     */
    public void handleTaskPolling(FluxService.FluxPollingMessage pollingMessage) {
        String taskId = pollingMessage.getTaskId();
        String loginName = pollingMessage.getLoginName();
        Integer attemptCount = pollingMessage.getAttemptCount();

        log.debug("Processing Flux task polling for taskId: {}, attempt: {}", taskId, attemptCount);

        try {
            // 检查任务是否还存在于Redis中
            String taskKey = FLUX_TASK_PREFIX + taskId;
            String markId = redisService.stringGet(taskKey);
            if (StringUtil.isBlank(markId)) {
                log.warn("Flux task not found in Redis, stopping polling: {}", taskId);
                return;
            }

            // 获取任务状态
            FluxResponse.TaskStatusResponse taskStatus = fluxService.getTaskResult(taskId);
            if (taskStatus == null) {
                log.warn("Failed to get Flux task status for taskId: {}", taskId);
                scheduleNextPolling(pollingMessage);
                return;
            }

            FluxTaskStatus status = FluxTaskStatus.fromStatus(taskStatus.getStatus());
            log.debug("Flux task status: taskId={}, status={}, progress={}", 
                    taskId, status, taskStatus.getProgress());

            // 更新Redis状态
            updateFluxTaskStatusInRedis(taskId, status, taskStatus.getProgress());

            // 根据状态处理
            switch (status) {
                case READY:
                    fluxService.handleTaskSuccess(taskId, loginName, taskStatus);
                    break;
                case ERROR:
                case CANCELLED:
                case TIMEOUT:
                case TASK_NOT_FOUND:
                    fluxService.handleTaskFailure(taskId, loginName);
                    break;
                case PENDING:
                case RUNNING:
                    // 继续轮询
                    scheduleNextPolling(pollingMessage);
                    break;
                default:
                    log.warn("Unknown Flux task status: {}", status);
                    scheduleNextPolling(pollingMessage);
            }

        } catch (Exception e) {
            log.error("Handle Flux task polling error for taskId: " + taskId, e);
            scheduleNextPolling(pollingMessage);
        }
    }

    /**
     * 更新Flux任务状态到Redis
     */
    private void updateFluxTaskStatusInRedis(String taskId, FluxTaskStatus status, Integer progress) {
        try {
            String markId = redisService.stringGet(taskId);
            if (StringUtil.isBlank(markId)) {
                return;
            }

            String loginName = redisService.stringGet(markId);
            if (StringUtil.isBlank(loginName)) {
                return;
            }

            // 根据状态设置Redis值
            int redisStatus;
            switch (status) {
                case PENDING:
                    redisStatus = 1; // 排队中
                    break;
                case RUNNING:
                    redisStatus = 0; // 执行中
                    break;
                case READY:
                    redisStatus = 3; // 成功
                    break;
                case ERROR:
                case CANCELLED:
                case TIMEOUT:
                case TASK_NOT_FOUND:
                    redisStatus = 2; // 失败
                    break;
                default:
                    redisStatus = -1; // 未知
            }

            redisService.putDataToHash(loginName, markId, redisStatus, 2, TimeUnit.HOURS);

            log.debug("Updated Flux task status in Redis: taskId={}, status={}, redisStatus={}", 
                    taskId, status, redisStatus);

        } catch (Exception e) {
            log.error("Update Flux task status in Redis error", e);
        }
    }

    /**
     * 安排下次轮询
     */
    private void scheduleNextPolling(FluxService.FluxPollingMessage pollingMessage) {
        try {
            int nextAttempt = pollingMessage.getAttemptCount() + 1;
            int maxAttempts = fluxConfig.getMaxPollingAttempts() != null ? 
                    fluxConfig.getMaxPollingAttempts() : 100;

            if (nextAttempt >= maxAttempts) {
                log.warn("Flux task polling reached max attempts, stopping: taskId={}, attempts={}", 
                        pollingMessage.getTaskId(), nextAttempt);
                fluxService.handleTaskFailure(pollingMessage.getTaskId(), pollingMessage.getLoginName());
                return;
            }

            FluxService.FluxPollingMessage nextMessage = new FluxService.FluxPollingMessage(
                    pollingMessage.getTaskId(),
                    pollingMessage.getLoginName(),
                    nextAttempt
            );

            String messageBody = JsonUtils.writeToString(nextMessage);
            int delaySeconds = fluxConfig.getPollingIntervalSeconds() != null ? 
                    fluxConfig.getPollingIntervalSeconds() : 3;

            normalMessageProducer.delaySend(fluxPollingTopic, fluxPollingTag, messageBody, delaySeconds);

            log.debug("Scheduled next Flux polling: taskId={}, attempt={}, delay={}s", 
                    pollingMessage.getTaskId(), nextAttempt, delaySeconds);

        } catch (Exception e) {
            log.error("Schedule next Flux polling error", e);
        }
    }

    /**
     * 清理Redis中的任务数据
     */
    private void cleanupTaskFromRedis(String taskId, String markId, String loginName) {
        try {
            if (StringUtil.isNotBlank(taskId)) {
                redisService.stringDelete(taskId);
                String taskKey = FLUX_TASK_PREFIX + taskId;
                redisService.stringDelete(taskKey);
            }

            if (StringUtil.isNotBlank(markId)) {
                redisService.stringDelete(markId);
                redisService.deleteFieldFromHash(loginName, markId);
            }

            log.debug("Cleaned up Redis data for Flux task: taskId={}, markId={}", taskId, markId);

        } catch (Exception e) {
            log.error("Cleanup Redis data error", e);
        }
    }
}
