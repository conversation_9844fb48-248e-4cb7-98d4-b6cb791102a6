package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.RedisService.USER_TODAY_CREATE_IMG_NUMS;

/**
 * Flux图像处理结果服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxImageProcessResultService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private FluxService fluxService;

    private static final String FLUX_USER_CONCURRENT_PREFIX = "flux:concurrent";

    /**
     * 处理Flux图像处理结果
     */
    public void handleImageProcessResult(FluxService.FluxImageProcessMessage message) {
        String taskId = message.getTaskId();
        String loginName = message.getLoginName();

        log.info("Processing Flux image process result for taskId: {}, loginName: {}", taskId, loginName);

        try {
            // 获取markId
            String markId = redisService.stringGet(taskId);
            if (StringUtil.isBlank(markId)) {
                log.warn("MarkId not found for Flux taskId: {}", taskId);
                return;
            }

            // 更新PromptRecord状态
            updatePromptRecord(taskId, loginName);

            // 保存图像文件信息
            savePromptFile(message, markId);

            // 更新用户当日生图数量
            updateUserTodayCreateImgNums(loginName, 1);

            // 更新VIP相关统计
            updateVipStatistics(taskId, markId, loginName);

            // 更新Redis状态为成功
            redisService.putDataToHash(loginName, markId, 3, 2, TimeUnit.HOURS); // 3表示成功

            // 移除并发任务
            fluxService.removeFluxConcurrentJob(loginName, taskId);

            // 清理Redis数据
            cleanupTaskFromRedis(taskId, markId, loginName);

            log.info("Successfully processed Flux image result for taskId: {}", taskId);

        } catch (Exception e) {
            log.error("Handle Flux image process result error for taskId: " + taskId, e);
            
            // 处理失败，更新状态
            try {
                String markId = redisService.stringGet(taskId);
                if (StringUtil.isNotBlank(markId)) {
                    redisService.putDataToHash(loginName, markId, 2, 2, TimeUnit.HOURS); // 2表示失败
                }
                fluxService.removeFluxConcurrentJob(loginName, taskId);
            } catch (Exception cleanupError) {
                log.error("Cleanup after error failed", cleanupError);
            }
        }
    }

    /**
     * 更新PromptRecord状态
     */
    private void updatePromptRecord(String taskId, String loginName) {
        try {
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, taskId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getGenEndTime, LocalDateTime.now());

            int updated = promptRecordMapper.update(null, updateWrapper);
            if (updated > 0) {
                log.debug("Updated PromptRecord for Flux taskId: {}", taskId);
            } else {
                log.warn("No PromptRecord found to update for Flux taskId: {}", taskId);
            }

        } catch (Exception e) {
            log.error("Update PromptRecord error for taskId: " + taskId, e);
        }
    }

    /**
     * 保存图像文件信息
     */
    private void savePromptFile(FluxService.FluxImageProcessMessage message, String markId) {
        try {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(message.getLoginName());
            promptFile.setPromptId(message.getTaskId());
            promptFile.setFileUrl(message.getImageUrl());
            promptFile.setThumbnailUrl(message.getImageUrl()); // Flux返回的就是处理好的图片
            
            if (message.getWidth() != null) {
                promptFile.setWidth(message.getWidth());
            }
            if (message.getHeight() != null) {
                promptFile.setHeight(message.getHeight());
            }
            
            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(message.getLoginName());

            promptFileMapper.insert(promptFile);
            log.debug("Saved PromptFile for Flux taskId: {}", message.getTaskId());

        } catch (Exception e) {
            log.error("Save PromptFile error for taskId: " + message.getTaskId(), e);
        }
    }

    /**
     * 更新用户当日生图数量
     */
    private void updateUserTodayCreateImgNums(String loginName, int imageCount) {
        try {
            Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, loginName);
            userTodayCreateImgNums = userTodayCreateImgNums != null ? userTodayCreateImgNums : 0;
            redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, loginName, userTodayCreateImgNums + imageCount);

            log.debug("Updated user today create img nums: loginName={}, count={}", 
                    loginName, userTodayCreateImgNums + imageCount);

        } catch (Exception e) {
            log.error("Update user today create img nums error", e);
        }
    }

    /**
     * 更新VIP相关统计
     */
    private void updateVipStatistics(String taskId, String markId, String loginName) {
        try {
            vipService.updateMessageByMarkId(markId, taskId, loginName, 1, 1, 1);
            log.debug("Updated VIP statistics for Flux taskId: {}", taskId);

        } catch (Exception e) {
            log.error("Update VIP statistics error for taskId: " + taskId, e);
        }
    }

    /**
     * 清理Redis中的任务数据
     */
    private void cleanupTaskFromRedis(String taskId, String markId, String loginName) {
        try {
            if (StringUtil.isNotBlank(taskId)) {
                redisService.stringDelete(taskId);
                String taskKey = "flux:task:" + taskId;
                redisService.stringDelete(taskKey);
            }

            if (StringUtil.isNotBlank(markId)) {
                redisService.stringDelete(markId);
                redisService.deleteFieldFromHash(loginName, markId);
                redisService.stringDelete("user:task:timestamp:" + markId);
            }

            log.debug("Cleaned up Redis data for Flux task: taskId={}, markId={}", taskId, markId);

        } catch (Exception e) {
            log.error("Cleanup Redis data error", e);
        }
    }
}
