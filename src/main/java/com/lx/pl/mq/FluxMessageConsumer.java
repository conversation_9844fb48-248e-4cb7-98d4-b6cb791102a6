package com.lx.pl.mq;

import com.lx.pl.service.FluxCallbackService;
import com.lx.pl.service.FluxImageProcessResultService;
import com.lx.pl.service.FluxService;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Flux消息消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FluxMessageConsumer {

    @Autowired
    private FluxCallbackService fluxCallbackService;

    @Autowired
    private FluxImageProcessResultService fluxImageProcessResultService;

    /**
     * Flux任务状态轮询消息消费者
     */
    @Component
    @RocketMQMessageListener(
            topic = "${rocketmq.flux.polling.topic:tp_flux_polling_test}",
            selectorExpression = "${rocketmq.flux.polling.tag:tag_flux_polling_test}",
            consumerGroup = "${rocketmq.flux.polling.consumer.group:cg_flux_polling_test}"
    )
    public static class FluxPollingConsumer implements RocketMQListener<String> {

        @Autowired
        private FluxCallbackService fluxCallbackService;

        @Override
        public void onMessage(String message) {
            log.debug("Received Flux polling message: {}", message);

            try {
                FluxService.FluxPollingMessage pollingMessage = JsonUtils.readValue(
                        message, FluxService.FluxPollingMessage.class);

                if (pollingMessage == null || pollingMessage.getTaskId() == null) {
                    log.warn("Invalid Flux polling message: {}", message);
                    return;
                }

                fluxCallbackService.handleTaskPolling(pollingMessage);

            } catch (Exception e) {
                log.error("Process Flux polling message error: " + message, e);
            }
        }
    }

    /**
     * Flux图像处理结果消息消费者
     */
    @Component
    @RocketMQMessageListener(
            topic = "${rocketmq.piclumen.topic}",
            selectorExpression = "${rocketmq.image.process.tag}",
            consumerGroup = "${rocketmq.flux.image.consumer.group:cg_flux_image_process}"
    )
    public static class FluxImageProcessConsumer implements RocketMQListener<String> {

        @Autowired
        private FluxImageProcessResultService fluxImageProcessResultService;

        @Override
        public void onMessage(String message) {
            log.debug("Received Flux image process message: {}", message);

            try {
                FluxService.FluxImageProcessMessage imageMessage = JsonUtils.readValue(
                        message, FluxService.FluxImageProcessMessage.class);

                if (imageMessage == null || imageMessage.getTaskId() == null) {
                    log.warn("Invalid Flux image process message: {}", message);
                    return;
                }

                fluxImageProcessResultService.handleImageProcessResult(imageMessage);

            } catch (Exception e) {
                log.error("Process Flux image process message error: " + message, e);
            }
        }
    }
}
