package com.lx.pl.dto.flux;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Flux API响应类
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FluxResponse {

    /**
     * 任务ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 轮询URL
     */
    @JsonProperty("polling_url")
    private String pollingUrl;

    /**
     * 任务状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 结果数据
     */
    @JsonProperty("result")
    private Object result;

    /**
     * 进度（0-100）
     */
    @JsonProperty("progress")
    private Integer progress;

    /**
     * 详细信息
     */
    @JsonProperty("details")
    private Map<String, Object> details;

    /**
     * 创建任务响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CreateTaskResponse {
        /**
         * 任务ID
         */
        @JsonProperty("id")
        private String id;

        /**
         * 轮询URL
         */
        @JsonProperty("polling_url")
        private String pollingUrl;
    }

    /**
     * 任务结果数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskResult {
        /**
         * 生成的图像URL列表
         */
        @JsonProperty("sample")
        private String sample;

        /**
         * 图像宽度
         */
        @JsonProperty("width")
        private Integer width;

        /**
         * 图像高度
         */
        @JsonProperty("height")
        private Integer height;

        /**
         * 种子值
         */
        @JsonProperty("seed")
        private Integer seed;

        /**
         * 提示词
         */
        @JsonProperty("prompt")
        private String prompt;
    }

    /**
     * 任务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatusResponse {
        /**
         * 任务ID
         */
        @JsonProperty("id")
        private String id;

        /**
         * 任务状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 结果数据
         */
        @JsonProperty("result")
        private TaskResult result;

        /**
         * 进度
         */
        @JsonProperty("progress")
        private Integer progress;

        /**
         * 详细信息
         */
        @JsonProperty("details")
        private Map<String, Object> details;
    }

    /**
     * 错误响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorResponse {
        /**
         * 错误代码
         */
        @JsonProperty("error")
        private String error;

        /**
         * 错误消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 详细信息
         */
        @JsonProperty("details")
        private Map<String, Object> details;
    }
}
