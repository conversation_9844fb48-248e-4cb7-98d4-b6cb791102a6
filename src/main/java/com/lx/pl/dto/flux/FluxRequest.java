package com.lx.pl.dto.flux;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Flux API请求基础类
 *
 * <AUTHOR>
 */
@Data
public class FluxRequest {

    /**
     * 提示词
     */
    @NotBlank(message = "提示词不能为空")
    @JsonProperty("prompt")
    private String prompt;

    /**
     * 输入图像（Base64编码，用于图像编辑）
     */
    @JsonProperty("input_image")
    private String inputImage;

    /**
     * 种子值
     */
    @JsonProperty("seed")
    private Long seed;

    /**
     * 宽高比
     */
    @JsonProperty("aspect_ratio")
    private String aspectRatio;

    /**
     * 输出格式：jpeg/png
     */
    @JsonProperty("output_format")
    private String outputFormat = "jpeg";

    /**
     * Webhook URL
     */
    @JsonProperty("webhook_url")
    private String webhookUrl;

    /**
     * Webhook密钥
     */
    @JsonProperty("webhook_secret")
    private String webhookSecret;

    /**
     * 是否启用prompt增强
     */
    @JsonProperty("prompt_upsampling")
    private Boolean promptUpsampling = false;

    /**
     * 安全容忍度（0-6）
     */
    @JsonProperty("safety_tolerance")
    private Integer safetyTolerance = 2;

    /**
     * Kontext Pro图像生成请求
     */
    @Data
    public static class KontextProRequest extends FluxRequest {
        // 继承所有基础字段
    }

    /**
     * 图像编辑请求
     */
    @Data
    public static class ImageEditRequest extends FluxRequest {
        /**
         * 编辑强度（0.0-1.0）
         */
        @JsonProperty("strength")
        private Double strength = 0.8;

        /**
         * 编辑指导
         */
        @JsonProperty("guidance")
        private Double guidance = 7.5;
    }
}
