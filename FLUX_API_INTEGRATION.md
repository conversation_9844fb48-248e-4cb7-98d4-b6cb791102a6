# Flux API 集成文档

本文档描述了如何在PicLumen项目中集成Black Forest Labs的Flux API。

## 概述

Flux API提供了先进的AI图像生成功能，包括：
- Flux Kontext Pro图像生成
- Flux Kontext Max图像生成  
- FLUX 1.1 Pro图像生成
- FLUX.1 Pro图像生成
- FLUX.1 Dev图像生成
- 图像编辑和增强

## 配置

### 1. 配置文件设置

在 `application.properties` 中添加以下配置：

```properties
# Flux API配置
flux.api.base-url=https://api.bfl.ai
flux.api.api-key=YOUR_BFL_API_KEY_HERE
flux.api.callback-url=https://your-domain.com/api/flux/callback/webhook
flux.api.default-timeout=300
flux.api.max-retries=3
flux.api.default-output-format=jpeg
flux.api.default-safety-tolerance=2
flux.api.prompt-upsampling-enabled=false
flux.api.max-concurrent-jobs=10
flux.api.polling-interval-seconds=3
flux.api.first-delay-seconds=10
flux.api.max-polling-attempts=100
flux.api.balance-alarm-threshold=10.0

# Flux模型ID
flux.modelId=flux-kontext-pro

# Flux RocketMQ配置
rocketmq.flux.polling.topic=tp_flux_polling_dev
rocketmq.flux.polling.group=gid_flux_polling_dev
rocketmq.flux.polling.tag=tag_flux_polling_dev
rocketmq.flux.polling.consumer.group=cg_flux_polling_dev
rocketmq.flux.image.consumer.group=cg_flux_image_process_dev
```

### 2. 获取API密钥

1. 访问 [Black Forest Labs](https://bfl.ai) 官网
2. 注册账户并获取API密钥
3. 将API密钥配置到 `flux.api.api-key` 中

## 架构设计

### 核心组件

1. **FluxConfig** - 配置管理
2. **FluxApiClient** - API客户端接口
3. **FluxService** - 核心业务逻辑
4. **FluxController** - REST API控制器
5. **FluxCallbackService** - 任务状态轮询处理
6. **FluxImageProcessResultService** - 图像处理结果服务

### 数据流程

1. 用户发起图像生成请求
2. FluxController接收请求并验证参数
3. FluxService调用Flux API创建任务
4. 保存任务信息到数据库和Redis
5. 启动RocketMQ延迟队列进行状态轮询
6. FluxCallbackService处理轮询结果
7. 任务完成后处理图像并更新数据库

## API接口

### 1. Flux Kontext Pro图像生成

```http
POST /api/flux/kontext-pro
Authorization: Bearer {token}
Content-Type: application/json

{
    "prompt": "A beautiful landscape with mountains and lakes",
    "aspectRatio": "16:9",
    "seed": 42,
    "outputFormat": "jpeg",
    "promptUpsampling": false,
    "safetyTolerance": 2
}
```

### 2. 获取任务结果

```http
GET /api/flux/result/{taskId}
Authorization: Bearer {token}
```

### 3. 兼容原接口的图像生成

```http
POST /api/flux/create
Authorization: Bearer {token}
Content-Type: application/json

{
    "prompt": "A beautiful landscape",
    "aspectRatio": "16:9",
    "seed": 42,
    "fastHour": false,
    "platform": "web"
}
```

### 4. 批量任务状态查询

```http
POST /api/flux/batchProcessTask
Authorization: Bearer {token}
Content-Type: application/json

{
    "markId": "tmj-uuid-here"
}
```

## 状态管理

### Redis状态

- **任务映射**: `taskId -> markId`
- **用户映射**: `markId -> loginName`
- **任务状态**: `loginName -> {markId: status}`
- **并发控制**: `flux:concurrent -> {taskId: timestamp}`

### 状态值说明

- `-1`: 新建状态
- `0`: 执行中
- `1`: 排队中
- `2`: 失败
- `3`: 成功

## 错误处理

### 错误码

- `4026`: Flux API调用失败
- `4027`: Flux图像生成失败
- `4028`: Flux超过并发任务限制
- `4029`: Flux任务未找到
- `4030`: Flux参数无效

### 重试机制

- API调用失败自动重试（最大3次）
- 状态轮询失败继续下次轮询
- 最大轮询次数100次（约5分钟）

## 监控和告警

### 余额监控

当API余额低于阈值时，系统会发送告警通知。

### 并发控制

- 默认最大并发任务数：10个
- 可通过配置调整：`flux.api.max-concurrent-jobs`

### 任务超时

- 默认轮询超时：100次 × 3秒 = 5分钟
- 可通过配置调整轮询间隔和最大次数

## 部署说明

### 环境要求

- Java 8+
- Spring Boot 2.x
- Redis
- RocketMQ
- MySQL

### 部署步骤

1. 更新配置文件中的API密钥
2. 确保RocketMQ主题和消费组已创建
3. 重启应用服务
4. 验证API接口可用性

## 测试

### 单元测试

```bash
mvn test -Dtest=FluxServiceTest
```

### 集成测试

```bash
curl -X POST "http://localhost:8080/api/flux/kontext-pro" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over the ocean",
    "aspectRatio": "16:9"
  }'
```

## 故障排查

### 常见问题

1. **API密钥无效**
   - 检查配置文件中的API密钥
   - 确认密钥有效期和权限

2. **任务状态轮询失败**
   - 检查RocketMQ连接
   - 查看消费者组状态

3. **并发限制**
   - 检查Redis中的并发任务数
   - 调整最大并发配置

### 日志查看

```bash
# 查看Flux相关日志
grep "Flux" application.log

# 查看API调用日志
grep "FluxApiClient" application.log
```

## 性能优化

### 建议配置

- 生产环境并发数：30个
- 轮询间隔：3秒
- 最大轮询次数：100次

### 缓存策略

- 任务状态缓存2小时
- 用户并发任务缓存2小时
- 图像处理锁5分钟

## 版本历史

- v1.0.0: 初始版本，支持Flux Kontext Pro
- v1.1.0: 添加图像编辑功能
- v1.2.0: 优化轮询机制和错误处理
